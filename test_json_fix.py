#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSON解析修复
"""

import json
import sys
from pathlib import Path

def test_json_parsing():
    """测试JSON解析功能"""
    print("🧪 测试JSON解析修复...")
    
    # 测试日志文件路径
    log_file = Path("logs/requests.jsonl")
    
    if not log_file.exists():
        print("❌ 日志文件不存在")
        return False
    
    success_count = 0
    error_count = 0
    empty_lines = 0
    
    print(f"📖 读取日志文件: {log_file}")
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        print(f"📊 总行数: {len(lines)}")
        
        for i, line in enumerate(lines, 1):
            line = line.strip()
            
            if not line:
                empty_lines += 1
                print(f"⚠️  第{i}行: 空行")
                continue
                
            try:
                data = json.loads(line)
                success_count += 1
                print(f"✅ 第{i}行: JSON解析成功 - {data.get('type', 'unknown')}")
            except json.JSONDecodeError as e:
                error_count += 1
                print(f"❌ 第{i}行: JSON解析失败 - {e}")
                print(f"   内容: {line[:100]}...")
    
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False
    
    print(f"\n📈 统计结果:")
    print(f"   ✅ 成功解析: {success_count}")
    print(f"   ❌ 解析失败: {error_count}")
    print(f"   ⚠️  空行数量: {empty_lines}")
    
    if error_count == 0:
        print("🎉 所有JSON行都解析成功！")
        return True
    else:
        print("⚠️  存在JSON解析错误")
        return False

def test_log_manager():
    """测试LogManager的读取功能"""
    print("\n🧪 测试LogManager读取功能...")
    
    try:
        # 导入LogManager类
        sys.path.append('.')
        from proxy_server import LogManager
        
        log_manager = LogManager()
        
        # 测试读取请求日志
        print("📖 测试读取请求日志...")
        request_logs = log_manager.read_request_logs(limit=10)
        print(f"✅ 成功读取 {len(request_logs)} 条请求日志")
        
        # 测试读取错误日志
        print("📖 测试读取错误日志...")
        error_logs = log_manager.read_error_logs(limit=10)
        print(f"✅ 成功读取 {len(error_logs)} 条错误日志")
        
        return True
        
    except Exception as e:
        print(f"❌ LogManager测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 JSON解析修复测试")
    print("=" * 50)
    
    # 测试基本JSON解析
    json_test_passed = test_json_parsing()
    
    # 测试LogManager
    log_manager_test_passed = test_log_manager()
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print(f"   JSON解析测试: {'✅ 通过' if json_test_passed else '❌ 失败'}")
    print(f"   LogManager测试: {'✅ 通过' if log_manager_test_passed else '❌ 失败'}")
    
    if json_test_passed and log_manager_test_passed:
        print("🎉 所有测试通过！JSON解析问题已修复。")
        sys.exit(0)
    else:
        print("⚠️  部分测试失败，请检查修复。")
        sys.exit(1)
